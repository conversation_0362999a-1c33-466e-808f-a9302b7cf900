import { useState, useCallback } from "react";
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";

interface StreamingOptions {
  conversationId: Id<"conversations">;
  messages: Array<{
    role: "user" | "assistant" | "system";
    content: string;
  }>;
  provider?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  enabledTools?: string[];
}

export function useStreamingResponse() {
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingContent, setStreamingContent] = useState("");
  const [currentMessageId, setCurrentMessageId] =
    useState<Id<"messages"> | null>(null);

  const addMessage = useMutation(api.messages.add);
  const updateMessage = useMutation(api.messages.update);

  const startStreaming = useCallback(
    async (options: StreamingOptions) => {
      setIsStreaming(true);
      setStreamingContent("");

      try {
        // Create an initial empty assistant message
        const messageId = await addMessage({
          conversationId: options.conversationId,
          role: "assistant",
          content: "",
        });

        setCurrentMessageId(messageId);

        // Get the Convex site URL for the streaming endpoint
        const convexUrl =
          process.env.NEXT_PUBLIC_CONVEX_URL?.replace("/api", "") ||
          "http://localhost:3000";
        const streamUrl = `${convexUrl}/ai/stream`;

        // Start the streaming request
        const response = await fetch(streamUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            conversationId: options.conversationId,
            messages: options.messages,
            provider: options.provider || "openai",
            model: options.model,
            temperature: options.temperature || 0.7,
            maxTokens: options.maxTokens || 1000,
            enabledTools: options.enabledTools || [],
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error("No reader available");
        }

        const decoder = new TextDecoder();
        let accumulatedContent = "";

        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split("\n");

          for (const line of lines) {
            if (line.startsWith("0:")) {
              // AI SDK streaming format
              const data = line.slice(2);
              if (data.trim()) {
                try {
                  const content = JSON.parse(data);
                  if (typeof content === "string") {
                    accumulatedContent += content;
                    setStreamingContent(accumulatedContent);
                  }
                } catch (e) {
                  // If it's not JSON, treat as plain text
                  accumulatedContent += data;
                  setStreamingContent(accumulatedContent);
                }
              }
            }
          }
        }

        // Final update to ensure the message is saved
        if (accumulatedContent) {
          await updateMessage({
            messageId,
            content: accumulatedContent,
          });
        }
      } catch (error) {
        console.error("Streaming error:", error);

        // Save error message if we have a message ID
        if (currentMessageId) {
          await updateMessage({
            messageId: currentMessageId,
            content: `I apologize, but I encountered an error: ${error instanceof Error ? error.message : "Unknown error"}`,
          });
        }

        throw error;
      } finally {
        setIsStreaming(false);
        setStreamingContent("");
        setCurrentMessageId(null);
      }
    },
    [addMessage, updateMessage]
  );

  return {
    isStreaming,
    streamingContent,
    currentMessageId,
    startStreaming,
  };
}
