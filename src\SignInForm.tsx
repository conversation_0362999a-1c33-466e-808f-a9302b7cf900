"use client";
import { useAuthActions } from "@convex-dev/auth/react";
import { useState } from "react";
import { toast } from "sonner";
import { Mail, KeyRound, LoaderCircle, User } from "lucide-react";

export function SignInForm() {
  const { signIn } = useAuthActions();
  const [flow, setFlow] = useState<"signIn" | "signUp">("signIn");
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSubmitting(true);
    const formData = new FormData(e.target as HTMLFormElement);
    formData.set("flow", flow);

    signIn("password", formData).catch((error) => {
      let toastTitle = "";
      if (error.message.includes("Invalid password")) {
        toastTitle = "Invalid password. Please try again.";
      } else {
        toastTitle =
          flow === "signIn"
            ? "Could not sign in. Do you need to sign up?"
            : "Could not sign up. Do you already have an account?";
      }
      toast.error(toastTitle);
    }).finally(() => {
      setSubmitting(false);
    });
  };

  return (
    <div className="w-full max-w-md mx-auto bg-white p-8 rounded-2xl shadow-lg">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-800">
          {flow === "signIn" ? "Welcome Back" : "Create an Account"}
        </h2>
        <p className="text-gray-500 mt-2">
          {flow === "signIn"
            ? "Sign in to continue."
            : "Get started in just a few clicks."}
        </p>
      </div>

      <form className="space-y-6" onSubmit={handleSubmit}>
        {/* Email Input */}
        <div className="relative">
          <label htmlFor="email" className="sr-only">Email</label>
          <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            id="email"
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
            type="email"
            name="email"
            placeholder="Email"
            required
          />
        </div>

        {/* Password Input */}
        <div className="relative">
          <label htmlFor="password" className="sr-only">Password</label>
          <KeyRound className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            id="password"
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
            type="password"
            name="password"
            placeholder="Password"
            required
          />
        </div>

        {/* Submit Button */}
        <button
          className="w-full flex items-center justify-center bg-indigo-600 text-white font-semibold py-3 px-4 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-300 disabled:bg-indigo-400 disabled:cursor-not-allowed"
          type="submit"
          disabled={submitting}
        >
          {submitting ? (
            <LoaderCircle className="animate-spin h-6 w-6" />
          ) : (
            <span>{flow === "signIn" ? "Sign In" : "Sign Up"}</span>
          )}
        </button>
      </form>

      {/* Flow Switcher */}
      <div className="text-center text-sm text-gray-600 mt-6">
        <span>
          {flow === "signIn"
            ? "Don't have an account? "
            : "Already have an account? "}
        </span>
        <button
          type="button"
          className="text-indigo-600 hover:text-indigo-500 hover:underline font-medium"
          onClick={() => setFlow(flow === "signIn" ? "signUp" : "signIn")}
        >
          {flow === "signIn" ? "Sign up" : "Sign in"}
        </button>
      </div>

      {/* "Or" Divider */}
      <div className="flex items-center my-6">
        <hr className="flex-grow border-t border-gray-300" />
        <span className="mx-4 text-xs font-semibold text-gray-400 uppercase">
          OR
        </span>
        <hr className="flex-grow border-t border-gray-300" />
      </div>

      {/* Anonymous Sign In */}
      <button
        className="w-full flex items-center justify-center gap-2 bg-white text-gray-700 font-semibold py-3 px-4 rounded-lg border border-gray-300 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 transition-colors duration-300"
        onClick={() => void signIn("anonymous")}
      >
        <User className="h-5 w-5 text-gray-500" />
        <span>Sign in anonymously</span>
      </button>
    </div>
  );
}