"use node";

import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";
import { streamText } from "ai";
import { openai } from "@ai-sdk/openai";
import { anthropic } from "@ai-sdk/anthropic";
import { google } from "@ai-sdk/google";
import { api } from "./_generated/api";

const http = httpRouter();

http.route({
  path: "/ai/stream",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const body = await request.json();
    const {
      conversationId,
      messages,
      provider = "openai",
      model,
      temperature = 0.7,
      maxTokens = 1000,
      enabledTools = [],
    } = body;

    // Get user's API key for the provider
    const apiKeyRecord = await ctx.runQuery(api.apiKeys.getByProvider, {
      provider,
    });

    let aiModel;
    let apiKey = "";
    let baseURL = "";
    let usingUserKey = false;

    // Provider configurations (simplified for streaming)
    const PROVIDER_CONFIGS: Record<string, any> = {
      openai: {
        models: ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-3.5-turbo"],
        baseURL: "https://api.openai.com/v1",
        apiKey: process.env.OPENAI_API_KEY,
      },
      anthropic: {
        models: ["claude-3-5-sonnet-20241022", "claude-3-haiku-20240307"],
        apiKey: process.env.ANTHROPIC_API_KEY,
      },
      google: {
        models: ["gemini-2.0-flash", "gemini-1.5-pro", "gemini-1.5-flash"],
        apiKey: process.env.GOOGLE_API_KEY,
      },
    };

    if (apiKeyRecord?.apiKey) {
      apiKey = apiKeyRecord.apiKey;
      usingUserKey = true;
    } else {
      const config = PROVIDER_CONFIGS[provider];
      if (!config?.apiKey) {
        throw new Error(`No API key available for provider: ${provider}`);
      }
      apiKey = config.apiKey;
      baseURL = config.baseURL || "";
    }

    // Create AI model instance
    if (provider === "openai") {
      aiModel = openai(model, { baseURL, apiKey });
    } else if (provider === "anthropic") {
      aiModel = anthropic(model, { apiKey });
    } else if (provider === "google") {
      aiModel = google(model, { apiKey });
    } else {
      throw new Error(`Unsupported provider: ${provider}`);
    }

    // Stream the response
    const result = await streamText({
      model: aiModel,
      messages,
      temperature,
      maxTokens,
    });

    // Return the stream response
    return result.toDataStreamResponse();
  }),
});

export default http;
